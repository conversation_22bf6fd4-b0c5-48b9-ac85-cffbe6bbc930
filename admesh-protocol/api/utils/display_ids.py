"""
Utility functions for generating and managing display IDs for offers and agents.
Display IDs are user-friendly, unique identifiers that can be safely exposed in URLs and UIs.
"""

import re
import random
import string
from typing import Optional
from firebase.config import get_db
from slugify import slugify

db = get_db()

def generate_random_suffix(length: int = 6) -> str:
    """Generate a random alphanumeric suffix"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def clean_name_for_id(name: str) -> str:
    """Clean and format a name for use in display ID"""
    if not name:
        return "unnamed"
    
    # Use slugify to create a clean, URL-safe string
    cleaned = slugify(name, max_length=20, word_boundary=True)
    
    # If slugify results in empty string, use fallback
    if not cleaned:
        return "unnamed"
    
    return cleaned

def generate_offer_display_id(offer_title: str, brand_name: Optional[str] = None) -> str:
    """
    Generate a display ID for an offer
    Format: offer-{cleaned_title}-{random_suffix}
    Example: offer-admesh-platform-a1b2c3
    """
    cleaned_title = clean_name_for_id(offer_title)
    suffix = generate_random_suffix()
    
    return f"offer-{cleaned_title}-{suffix}"

def generate_agent_display_id(agent_name: str) -> str:
    """
    Generate a display ID for an agent
    Format: agent-{cleaned_name}-{random_suffix}
    Example: agent-john-smith-x9y8z7
    """
    cleaned_name = clean_name_for_id(agent_name)
    suffix = generate_random_suffix()
    
    return f"agent-{cleaned_name}-{suffix}"

def is_display_id_unique(collection_name: str, field_name: str, display_id: str) -> bool:
    """Check if a display ID is unique in the specified collection"""
    try:
        query = db.collection(collection_name).where(field_name, "==", display_id).limit(1)
        docs = list(query.stream())
        return len(docs) == 0
    except Exception:
        return False

def ensure_unique_offer_display_id(offer_title: str, brand_name: Optional[str] = None, max_attempts: int = 10) -> str:
    """
    Generate a unique offer display ID, retrying if conflicts occur
    """
    for _ in range(max_attempts):
        display_id = generate_offer_display_id(offer_title, brand_name)
        if is_display_id_unique("offers", "offer_display_id", display_id):
            return display_id
    
    # Fallback: use timestamp-based suffix if all attempts fail
    import time
    timestamp_suffix = str(int(time.time()))[-6:]
    cleaned_title = clean_name_for_id(offer_title)
    return f"offer-{cleaned_title}-{timestamp_suffix}"

def ensure_unique_agent_display_id(agent_name: str, max_attempts: int = 10) -> str:
    """
    Generate a unique agent display ID, retrying if conflicts occur
    """
    for _ in range(max_attempts):
        display_id = generate_agent_display_id(agent_name)
        if is_display_id_unique("agents", "agent_display_id", display_id):
            return display_id
    
    # Fallback: use timestamp-based suffix if all attempts fail
    import time
    timestamp_suffix = str(int(time.time()))[-6:]
    cleaned_name = clean_name_for_id(agent_name)
    return f"agent-{cleaned_name}-{timestamp_suffix}"

def get_offer_by_display_id(display_id: str) -> Optional[dict]:
    """Get offer document by display ID"""
    try:
        query = db.collection("offers").where("offer_display_id", "==", display_id).limit(1)
        docs = list(query.stream())
        if docs:
            doc = docs[0]
            data = doc.to_dict()
            data["id"] = doc.id
            return data
        return None
    except Exception:
        return None

def get_agent_by_display_id(display_id: str) -> Optional[dict]:
    """Get agent document by display ID"""
    try:
        query = db.collection("agents").where("agent_display_id", "==", display_id).limit(1)
        docs = list(query.stream())
        if docs:
            doc = docs[0]
            data = doc.to_dict()
            data["id"] = doc.id
            return data
        return None
    except Exception:
        return None

def add_display_id_to_offer(offer_id: str, offer_data: dict) -> str:
    """
    Add display ID to an existing offer
    Returns the generated display ID
    """
    try:
        offer_title = offer_data.get("offer_title", offer_data.get("title", "Untitled"))
        brand_name = offer_data.get("brand_name")
        
        display_id = ensure_unique_offer_display_id(offer_title, brand_name)
        
        # Update the offer document
        db.collection("offers").document(offer_id).update({
            "offer_display_id": display_id
        })
        
        return display_id
    except Exception as e:
        print(f"Error adding display ID to offer {offer_id}: {e}")
        return ""

def add_display_id_to_agent(agent_id: str, agent_data: dict) -> str:
    """
    Add display ID to an existing agent
    Returns the generated display ID
    """
    try:
        agent_name = agent_data.get("name", "Unknown Agent")
        
        display_id = ensure_unique_agent_display_id(agent_name)
        
        # Update the agent document
        db.collection("agents").document(agent_id).update({
            "agent_display_id": display_id
        })
        
        return display_id
    except Exception as e:
        print(f"Error adding display ID to agent {agent_id}: {e}")
        return ""

def migrate_existing_offers():
    """
    Migration function to add display IDs to existing offers
    """
    try:
        offers_ref = db.collection("offers")
        
        # Get offers without display IDs
        query = offers_ref.where("offer_display_id", "==", None)
        offers_without_display_id = list(query.stream())
        
        print(f"Found {len(offers_without_display_id)} offers without display IDs")
        
        for offer_doc in offers_without_display_id:
            offer_data = offer_doc.to_dict()
            display_id = add_display_id_to_offer(offer_doc.id, offer_data)
            print(f"Added display ID '{display_id}' to offer {offer_doc.id}")
            
    except Exception as e:
        print(f"Error during offer migration: {e}")

def migrate_existing_agents():
    """
    Migration function to add display IDs to existing agents
    """
    try:
        agents_ref = db.collection("agents")
        
        # Get agents without display IDs
        query = agents_ref.where("agent_display_id", "==", None)
        agents_without_display_id = list(query.stream())
        
        print(f"Found {len(agents_without_display_id)} agents without display IDs")
        
        for agent_doc in agents_without_display_id:
            agent_data = agent_doc.to_dict()
            display_id = add_display_id_to_agent(agent_doc.id, agent_data)
            print(f"Added display ID '{display_id}' to agent {agent_doc.id}")
            
    except Exception as e:
        print(f"Error during agent migration: {e}")

# Validation functions
def is_valid_offer_display_id(display_id: str) -> bool:
    """Validate offer display ID format"""
    pattern = r'^offer-[a-z0-9-]+-[a-z0-9]{6}$'
    return bool(re.match(pattern, display_id))

def is_valid_agent_display_id(display_id: str) -> bool:
    """Validate agent display ID format"""
    pattern = r'^agent-[a-z0-9-]+-[a-z0-9]{6}$'
    return bool(re.match(pattern, display_id))

# Resolver functions for API endpoints
def resolve_offer_id(identifier: str) -> Optional[str]:
    """
    Resolve offer identifier to actual document ID
    If identifier is a display ID, return the actual document ID
    If identifier is already a document ID, return it as-is
    """
    # Check if it's a display ID format
    if is_valid_offer_display_id(identifier):
        offer = get_offer_by_display_id(identifier)
        return offer["id"] if offer else None

    # Assume it's a document ID and verify it exists
    try:
        offer_doc = db.collection("offers").document(identifier).get()
        return identifier if offer_doc.exists else None
    except Exception:
        return None

def resolve_agent_id(identifier: str) -> Optional[str]:
    """
    Resolve agent identifier to actual document ID
    If identifier is a display ID, return the actual document ID
    If identifier is already a document ID, return it as-is
    """
    # Check if it's a display ID format
    if is_valid_agent_display_id(identifier):
        agent = get_agent_by_display_id(identifier)
        return agent["id"] if agent else None

    # Assume it's a document ID and verify it exists
    try:
        agent_doc = db.collection("agents").document(identifier).get()
        return identifier if agent_doc.exists else None
    except Exception:
        return None
