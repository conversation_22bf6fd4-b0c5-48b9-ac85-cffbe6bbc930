"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  MessageCircle,
  Clock,
  MousePointerClick,
  ArrowUpRight,
  ChevronLeft,
  ChevronRight,
  Search,
  Calendar,
} from "lucide-react";
import DashboardFooter from "@/components/DashboardFooter";

interface QueryAnalytics {
  recommendation_id: string;
  query: string;
  timestamp: any;
  session_id: string;
  agent_id: string;
  user_id: string;
  recommended_offers: Array<{
    offer_id: string;
    offer_title: string;
    product_id: string;
    product_title: string;
  }>;
  clicks: number;
  conversions: number;
  created_at: any;
}

interface QueryAnalyticsResponse {
  queries: QueryAnalytics[];
  total: number;
  offset: number;
  limit: number;
  time_range: string;
  brand_id: string;
}

export default function BrandQueriesPage() {
  const { user } = useAuth();
  const [data, setData] = useState<QueryAnalyticsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("30d");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  const fetchQueryAnalytics = useCallback(async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);
      
      const token = await user.getIdToken();
      const offset = (currentPage - 1) * itemsPerPage;
      
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/queries/analytics?time_range=${timeRange}&limit=${itemsPerPage}&offset=${offset}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch query analytics");
      }

      const analyticsData = await response.json();
      setData(analyticsData);
    } catch (err) {
      console.error("Error fetching query analytics:", err);
      setError(err instanceof Error ? err.message : "Failed to load query analytics");
    } finally {
      setLoading(false);
    }
  }, [user, timeRange, currentPage]);

  useEffect(() => {
    fetchQueryAnalytics();
  }, [fetchQueryAnalytics]);

  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return "Unknown";
    
    let date: Date;
    if (timestamp.seconds) {
      // Firestore timestamp
      date = new Date(timestamp.seconds * 1000);
    } else if (typeof timestamp === "string") {
      date = new Date(timestamp);
    } else {
      date = new Date(timestamp);
    }
    
    return date.toLocaleDateString() + " " + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const totalPages = data ? Math.ceil(data.total / itemsPerPage) : 0;

  const handleTimeRangeChange = (newTimeRange: string) => {
    setTimeRange(newTimeRange);
    setCurrentPage(1); // Reset to first page when changing time range
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Query Analytics</h1>
            <p className="text-gray-600 mt-1">Track user queries that resulted in your product recommendations</p>
          </div>
        </div>
        
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="text-gray-600 mt-2">Loading query analytics...</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <DashboardFooter />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Query Analytics</h1>
            <p className="text-gray-600 mt-1">Track user queries that resulted in your product recommendations</p>
          </div>
        </div>
        
        <Card>
          <CardContent className="p-8">
            <div className="text-center">
              <div className="text-red-500 mb-2">
                <Search className="h-8 w-8 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Data</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={fetchQueryAnalytics}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
        
        <DashboardFooter />
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Query Analytics</h1>
          <p className="text-gray-600 mt-1">Track user queries that resulted in your product recommendations</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Calendar className="h-4 w-4 text-gray-500" />
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="all">All time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Stats Cards */}
      {data && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-600">Total Queries</CardTitle>
                <MessageCircle className="h-4 w-4 text-blue-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.total}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-600">Total Clicks</CardTitle>
                <MousePointerClick className="h-4 w-4 text-green-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data.queries.reduce((sum, q) => sum + q.clicks, 0)}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-600">Total Conversions</CardTitle>
                <ArrowUpRight className="h-4 w-4 text-orange-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data.queries.reduce((sum, q) => sum + q.conversions, 0)}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-gray-600">Avg. CTR</CardTitle>
                <Clock className="h-4 w-4 text-purple-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(() => {
                  const totalClicks = data.queries.reduce((sum, q) => sum + q.clicks, 0);
                  const totalConversions = data.queries.reduce((sum, q) => sum + q.conversions, 0);
                  const ctr = totalClicks > 0 ? (totalConversions / totalClicks * 100) : 0;
                  return `${ctr.toFixed(1)}%`;
                })()}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Query Analytics Table */}
      <Card>
        <CardHeader>
          <CardTitle>Query Details</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {data && data.queries.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[200px]">Query</TableHead>
                      <TableHead className="min-w-[150px]">Recommended Offers</TableHead>
                      <TableHead>Timestamp</TableHead>
                      <TableHead className="text-center">Clicks</TableHead>
                      <TableHead className="text-center">Conversions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.queries.map((query) => (
                      <TableRow key={query.recommendation_id}>
                        <TableCell>
                          <div className="max-w-[200px]">
                            <p className="font-medium text-gray-900 truncate" title={query.query}>
                              {query.query || "No query text"}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              Session: {query.session_id?.substring(0, 8)}...
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            {query.recommended_offers.map((offer, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {offer.offer_title}
                              </Badge>
                            ))}
                            {query.recommended_offers.length === 0 && (
                              <span className="text-gray-500 text-sm">No offers</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-600">
                            {formatTimestamp(query.timestamp)}
                          </span>
                        </TableCell>
                        <TableCell className="text-center">
                          <span className="font-medium">{query.clicks}</span>
                        </TableCell>
                        <TableCell className="text-center">
                          <span className="font-medium">{query.conversions}</span>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-6 py-4 border-t">
                  <div className="text-sm text-gray-600">
                    Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, data.total)} of {data.total} queries
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <span className="text-sm text-gray-600">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="p-8 text-center">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Query Data</h3>
              <p className="text-gray-600">
                No user queries have resulted in recommendations for your products in the selected time period.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <DashboardFooter />
    </div>
  );
}
